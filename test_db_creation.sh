#!/bin/bash

# Test script for Odoo database creation
# This script tests the POST /web/database/create endpoint

echo "🎯 Testing Odoo Database Creation"
echo "=================================="

# Configuration
SERVER_URL="http://localhost:8069"
MASTER_PASSWORD="ttl1034"
DATABASE_NAME="test_db_$(date +%s)"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"

echo "Server URL: $SERVER_URL"
echo "Master Password: $MASTER_PASSWORD"
echo "Database Name: $DATABASE_NAME"
echo "Admin Email: $ADMIN_EMAIL"
echo "=================================="

# Check if server is running
echo "🔍 Checking if server is running..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SERVER_URL/web/database/manager")

if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ Server is running (HTTP $HTTP_STATUS)"
else
    echo "❌ Server not accessible (HTTP $HTTP_STATUS)"
    echo "💡 Please start the server using: python odoo_runner.py"
    exit 1
fi

# Create database using POST request
echo "📊 Creating database: $DATABASE_NAME"
echo "⏳ This may take a few minutes..."

# Prepare form data
FORM_DATA="master_pwd=$MASTER_PASSWORD&name=$DATABASE_NAME&login=$ADMIN_EMAIL&password=$ADMIN_PASSWORD&lang=en_US&phone=%2B1234567890&country_code=US&demo=0"

# Send POST request
RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
    -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "$FORM_DATA" \
    "$SERVER_URL/web/database/create")

# Extract HTTP status and body
HTTP_STATUS=$(echo "$RESPONSE" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo "$RESPONSE" | sed -e 's/HTTPSTATUS:.*//g')

echo "📡 Response Status Code: $HTTP_STATUS"

# Check response
if [ "$HTTP_STATUS" = "303" ]; then
    echo "✅ Database created successfully! (Redirect response)"
    echo "🌐 Access your database at: $SERVER_URL"
    echo "📧 Login: $ADMIN_EMAIL"
    echo "🔑 Password: $ADMIN_PASSWORD"
    echo "🗄️  Database: $DATABASE_NAME"
    
    # Verify database exists
    echo "🔍 Verifying database exists..."
    DB_LIST_RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{}' \
        "$SERVER_URL/web/database/list")
    
    if echo "$DB_LIST_RESPONSE" | grep -q "$DATABASE_NAME"; then
        echo "✅ Database '$DATABASE_NAME' verified to exist!"
    else
        echo "⚠️  Database verification inconclusive"
        echo "Available databases: $DB_LIST_RESPONSE"
    fi
    
elif [ "$HTTP_STATUS" = "200" ]; then
    # Check for errors in response
    if echo "$RESPONSE_BODY" | grep -qi "error\|exception"; then
        echo "❌ Database creation failed - error in response:"
        echo "$RESPONSE_BODY" | head -20
    else
        echo "✅ Database created successfully!"
        echo "🌐 Access your database at: $SERVER_URL"
        echo "📧 Login: $ADMIN_EMAIL"
        echo "🔑 Password: $ADMIN_PASSWORD"
        echo "🗄️  Database: $DATABASE_NAME"
    fi
else
    echo "❌ Database creation failed with status: $HTTP_STATUS"
    echo "Response:"
    echo "$RESPONSE_BODY" | head -20
fi

echo ""
echo "🎉 Test completed!"
echo "💡 Server is still running. Use 'r' in odoo_runner to restart if needed."
