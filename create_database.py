#!/usr/bin/env python3
"""
Standalone script to create an Odoo database via POST request to /web/database/create
This script ensures the Odoo server is running and creates a database without errors.

Usage:
    python create_database.py

The script will:
1. Check if Odoo server is running
2. Start the server if needed using odoo_runner
3. Send POST request to create database
4. Verify database creation was successful
"""

import requests
import time
import subprocess
import sys
import json
from pathlib import Path
import signal
import os

class DatabaseCreator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.server_url = "http://localhost:8069"
        self.master_password = "ttl1034"
        self.database_name = "test_db_" + str(int(time.time()))  # Unique name
        self.admin_email = "<EMAIL>"
        self.admin_password = "admin123"
        self.odoo_runner_process = None
        
    def check_server_running(self):
        """Check if Odoo server is running by making a simple request"""
        try:
            response = requests.get(f"{self.server_url}/web/database/manager", timeout=10)
            # Accept various status codes that indicate server is running
            return response.status_code in [200, 303, 500]  # 500 might be temporary startup issue
        except requests.exceptions.RequestException:
            return False
    
    def start_odoo_server(self):
        """Start Odoo server using odoo_runner.py"""
        print("🚀 Starting Odoo server using odoo_runner.py...")
        
        try:
            # Start odoo_runner.py in a subprocess
            self.odoo_runner_process = subprocess.Popen(
                [sys.executable, "odoo_runner.py"],
                cwd=self.base_dir,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Send 's' command to start the server
            self.odoo_runner_process.stdin.write("s\n")
            self.odoo_runner_process.stdin.flush()
            
            print("⏳ Waiting for Odoo server to start...")
            
            # Wait for server to be ready (max 60 seconds)
            for i in range(60):
                if self.check_server_running():
                    print("✅ Odoo server is running!")
                    return True
                time.sleep(1)
                print(f"   Waiting... ({i+1}/60)")
            
            print("❌ Server failed to start within 60 seconds")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start Odoo server: {e}")
            return False
    
    def restart_server(self):
        """Restart the Odoo server using 'r' command"""
        if self.odoo_runner_process and self.odoo_runner_process.poll() is None:
            print("🔄 Restarting Odoo server...")
            try:
                self.odoo_runner_process.stdin.write("r\n")
                self.odoo_runner_process.stdin.flush()
                
                # Wait for restart to complete
                time.sleep(5)
                
                # Wait for server to be ready again
                for i in range(30):
                    if self.check_server_running():
                        print("✅ Odoo server restarted successfully!")
                        return True
                    time.sleep(1)
                
                print("❌ Server restart failed")
                return False
            except Exception as e:
                print(f"❌ Failed to restart server: {e}")
                return False
        else:
            print("⚠️  No running server process to restart")
            return False
    
    def create_database(self):
        """Send POST request to create database"""
        print(f"📊 Creating database: {self.database_name}")
        
        # Prepare the form data for database creation
        data = {
            'master_pwd': self.master_password,
            'name': self.database_name,
            'login': self.admin_email,
            'password': self.admin_password,
            'lang': 'en_US',
            'phone': '+1234567890',
            'country_code': 'US',
            'demo': '0'  # No demo data
        }
        
        try:
            # Send POST request to create database
            response = requests.post(
                f"{self.server_url}/web/database/create",
                data=data,
                allow_redirects=False,  # Don't follow redirects to see the actual response
                timeout=120  # Database creation can take time
            )
            
            print(f"📡 Response Status Code: {response.status_code}")
            print(f"📡 Response Headers: {dict(response.headers)}")
            
            # Check if database creation was successful
            if response.status_code == 303:  # Redirect indicates success
                redirect_location = response.headers.get('Location', '')
                if '/odoo' in redirect_location:
                    print("✅ Database created successfully!")
                    print(f"🎯 Redirect location: {redirect_location}")
                    return True
                else:
                    print(f"⚠️  Unexpected redirect location: {redirect_location}")
                    return False
            elif response.status_code == 200:
                # Check response content for errors
                if 'error' in response.text.lower():
                    print("❌ Database creation failed - error in response:")
                    print(response.text[:500])  # First 500 chars
                    return False
                else:
                    print("✅ Database created successfully!")
                    return True
            else:
                print(f"❌ Database creation failed with status code: {response.status_code}")
                print(f"Response content: {response.text[:500]}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ Database creation timed out (120 seconds)")
            return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
            return False
    
    def verify_database_exists(self):
        """Verify that the database was actually created"""
        print(f"🔍 Verifying database '{self.database_name}' exists...")
        
        try:
            # Get list of databases
            response = requests.post(
                f"{self.server_url}/web/database/list",
                headers={'Content-Type': 'application/json'},
                json={},
                timeout=10
            )
            
            if response.status_code == 200:
                databases = response.json().get('result', [])
                if self.database_name in databases:
                    print(f"✅ Database '{self.database_name}' verified to exist!")
                    return True
                else:
                    print(f"❌ Database '{self.database_name}' not found in database list")
                    print(f"Available databases: {databases}")
                    return False
            else:
                print(f"❌ Failed to get database list: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to verify database: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources"""
        if self.odoo_runner_process and self.odoo_runner_process.poll() is None:
            print("🧹 Cleaning up...")
            try:
                # Send exit command to odoo_runner
                self.odoo_runner_process.stdin.write("e\n")
                self.odoo_runner_process.stdin.flush()
                
                # Wait for graceful shutdown
                try:
                    self.odoo_runner_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't exit gracefully
                    self.odoo_runner_process.terminate()
                    try:
                        self.odoo_runner_process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        self.odoo_runner_process.kill()
                        
            except Exception as e:
                print(f"⚠️  Cleanup warning: {e}")
    
    def run(self):
        """Main execution flow"""
        print("🎯 Odoo Database Creator")
        print("=" * 50)
        print(f"Server URL: {self.server_url}")
        print(f"Master Password: {self.master_password}")
        print(f"Database Name: {self.database_name}")
        print(f"Admin Email: {self.admin_email}")
        print("=" * 50)
        
        try:
            # Check if server is already running
            if not self.check_server_running():
                if not self.start_odoo_server():
                    print("❌ Failed to start Odoo server")
                    return False
            else:
                print("✅ Odoo server is already running")
            
            # Create the database
            if not self.create_database():
                print("❌ Database creation failed")
                return False
            
            # Verify database exists
            if not self.verify_database_exists():
                print("❌ Database verification failed")
                return False
            
            print("\n🎉 SUCCESS! Database created and verified successfully!")
            print(f"🌐 You can access your database at: {self.server_url}")
            print(f"📧 Login: {self.admin_email}")
            print(f"🔑 Password: {self.admin_password}")
            print(f"🗄️  Database: {self.database_name}")
            
            return True
            
        except KeyboardInterrupt:
            print("\n⚠️  Operation cancelled by user")
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
        finally:
            # Note: We don't cleanup automatically to keep server running
            # User can manually stop the server using odoo_runner commands
            print("\n💡 Server is still running. Use odoo_runner.py to manage it.")

def main():
    """Entry point"""
    creator = DatabaseCreator()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\n⚠️  Interrupted by user")
        creator.cleanup()
        sys.exit(1)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    success = creator.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
