#!/usr/bin/env python3
"""
Simple script to create an Odoo database via POST request.
Assumes Odoo server is already running on localhost:8069.

Usage:
    python simple_create_db.py

Before running:
1. Start Odoo server using: python odoo_runner.py
2. In the odoo_runner prompt, type 's' to start the server
3. Wait for server to be ready, then run this script
"""

import requests
import time
import json

def create_database():
    """Create database using POST request to /web/database/create"""
    
    # Configuration
    server_url = "http://localhost:8069"
    master_password = "ttl1034"
    database_name = f"test_db_{int(time.time())}"  # Unique name
    admin_email = "<EMAIL>"
    admin_password = "admin123"
    
    print("🎯 Simple Odoo Database Creator")
    print("=" * 50)
    print(f"Server URL: {server_url}")
    print(f"Master Password: {master_password}")
    print(f"Database Name: {database_name}")
    print(f"Admin Email: {admin_email}")
    print("=" * 50)
    
    # Check if server is running
    print("🔍 Checking if server is running...")
    try:
        response = requests.get(f"{server_url}/web/database/manager", timeout=10)
        print(f"✅ Server responded with status: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Server not accessible: {e}")
        print("💡 Please start the server using: python odoo_runner.py")
        return False
    
    # Prepare form data for database creation
    data = {
        'master_pwd': master_password,
        'name': database_name,
        'login': admin_email,
        'password': admin_password,
        'lang': 'en_US',
        'phone': '+1234567890',
        'country_code': 'US',
        'demo': '0'  # No demo data
    }
    
    print(f"📊 Creating database: {database_name}")
    print("⏳ This may take a few minutes...")
    
    try:
        # Send POST request to create database
        response = requests.post(
            f"{server_url}/web/database/create",
            data=data,
            allow_redirects=False,  # Don't follow redirects
            timeout=300  # 5 minutes timeout for database creation
        )
        
        print(f"📡 Response Status Code: {response.status_code}")
        
        # Check response
        if response.status_code == 303:  # Redirect indicates success
            redirect_location = response.headers.get('Location', '')
            print(f"🎯 Redirect location: {redirect_location}")
            
            if '/odoo' in redirect_location:
                print("✅ Database created successfully!")
                print(f"🌐 Access your database at: {server_url}")
                print(f"📧 Login: {admin_email}")
                print(f"🔑 Password: {admin_password}")
                print(f"🗄️  Database: {database_name}")
                return True
            else:
                print(f"⚠️  Unexpected redirect: {redirect_location}")
                return False
                
        elif response.status_code == 200:
            # Check for errors in response content
            response_text = response.text
            if 'error' in response_text.lower() or 'exception' in response_text.lower():
                print("❌ Database creation failed - error in response:")
                # Try to extract error message
                if 'Database creation error:' in response_text:
                    start = response_text.find('Database creation error:')
                    end = response_text.find('</div>', start)
                    if end == -1:
                        end = start + 200
                    error_msg = response_text[start:end]
                    print(f"Error: {error_msg}")
                else:
                    print(response_text[:500])  # First 500 chars
                return False
            else:
                print("✅ Database created successfully!")
                print(f"🌐 Access your database at: {server_url}")
                print(f"📧 Login: {admin_email}")
                print(f"🔑 Password: {admin_password}")
                print(f"🗄️  Database: {database_name}")
                return True
                
        else:
            print(f"❌ Database creation failed with status: {response.status_code}")
            print("Response content:")
            print(response.text[:1000])  # First 1000 chars
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Database creation timed out (5 minutes)")
        print("💡 The database might still be creating in the background")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def verify_database_exists(database_name, server_url):
    """Verify that the database was created"""
    print(f"🔍 Verifying database '{database_name}' exists...")
    
    try:
        # Get list of databases
        response = requests.post(
            f"{server_url}/web/database/list",
            headers={'Content-Type': 'application/json'},
            json={},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            databases = result.get('result', [])
            print(f"📋 Available databases: {databases}")
            
            if database_name in databases:
                print(f"✅ Database '{database_name}' verified!")
                return True
            else:
                print(f"❌ Database '{database_name}' not found")
                return False
        else:
            print(f"❌ Failed to get database list: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to verify database: {e}")
        return False

def main():
    """Main function"""
    print("Starting database creation process...")
    
    success = create_database()
    
    if success:
        print("\n🎉 Database creation completed successfully!")
    else:
        print("\n❌ Database creation failed!")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure Odoo server is running: python odoo_runner.py")
        print("2. In odoo_runner, type 's' to start server")
        print("3. Wait for 'Odoo server started' message")
        print("4. If server has issues, try 'r' to restart")
        print("5. Check server logs for detailed error messages")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
