#!/usr/bin/env python3
"""
Minimal test script for database creation.
Run this after starting the server with odoo_runner.py
"""

import subprocess
import time

def test_database_creation():
    """Test database creation using curl subprocess"""
    
    # Configuration
    server_url = "http://localhost:8069"
    master_password = "ttl1034"
    database_name = f"test_db_{int(time.time())}"
    admin_email = "<EMAIL>"
    admin_password = "admin123"
    
    print("🎯 Testing Odoo Database Creation")
    print("=" * 50)
    print(f"Server URL: {server_url}")
    print(f"Master Password: {master_password}")
    print(f"Database Name: {database_name}")
    print(f"Admin Email: {admin_email}")
    print("=" * 50)
    
    # Test server connectivity
    print("🔍 Testing server connectivity...")
    try:
        result = subprocess.run([
            'curl', '-s', '-o', '/dev/null', '-w', '%{http_code}',
            f'{server_url}/web/database/manager'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and result.stdout.strip() == '200':
            print("✅ Server is accessible")
        else:
            print(f"❌ Server not accessible (HTTP {result.stdout.strip()})")
            return False
    except Exception as e:
        print(f"❌ Error testing connectivity: {e}")
        return False
    
    # Create database
    print(f"📊 Creating database: {database_name}")
    print("⏳ This may take a few minutes...")
    
    form_data = (
        f"master_pwd={master_password}&"
        f"name={database_name}&"
        f"login={admin_email}&"
        f"password={admin_password}&"
        f"lang=en_US&"
        f"phone=%2B1234567890&"
        f"country_code=US&"
        f"demo=0"
    )
    
    try:
        result = subprocess.run([
            'curl', '-s', '-w', 'HTTPSTATUS:%{http_code}',
            '-X', 'POST',
            '-H', 'Content-Type: application/x-www-form-urlencoded',
            '-d', form_data,
            f'{server_url}/web/database/create'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            print(f"❌ Curl command failed: {result.stderr}")
            return False
        
        # Parse response
        response = result.stdout
        if 'HTTPSTATUS:' in response:
            status_pos = response.rfind('HTTPSTATUS:')
            http_status = response[status_pos + 11:].strip()
            response_body = response[:status_pos]
        else:
            http_status = "unknown"
            response_body = response
        
        print(f"📡 Response Status Code: {http_status}")
        
        if http_status == '303':
            print("✅ Database created successfully! (Redirect response)")
            print(f"🌐 Access your database at: {server_url}")
            print(f"📧 Login: {admin_email}")
            print(f"🔑 Password: {admin_password}")
            print(f"🗄️  Database: {database_name}")
            return True
        elif http_status == '200':
            if 'error' in response_body.lower() or 'exception' in response_body.lower():
                print("❌ Database creation failed - error in response:")
                print(response_body[:500])
                return False
            else:
                print("✅ Database created successfully!")
                print(f"🌐 Access your database at: {server_url}")
                print(f"📧 Login: {admin_email}")
                print(f"🔑 Password: {admin_password}")
                print(f"🗄️  Database: {database_name}")
                return True
        else:
            print(f"❌ Database creation failed with status: {http_status}")
            print("Response:")
            print(response_body[:500])
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Database creation timed out (5 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error during database creation: {e}")
        return False

if __name__ == "__main__":
    success = test_database_creation()
    if success:
        print("\n🎉 Database creation test PASSED!")
    else:
        print("\n❌ Database creation test FAILED!")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure Odoo server is running: python odoo_runner.py")
        print("2. In odoo_runner, type 's' to start server")
        print("3. Wait for 'Uvicorn running' message")
        print("4. Check server logs for errors")
    
    exit(0 if success else 1)
